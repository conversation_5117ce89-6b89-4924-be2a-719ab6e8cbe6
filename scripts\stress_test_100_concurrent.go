package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

// BindCardRequest 绑卡请求
type BindCardRequest struct {
	MerchantID   uint   `json:"merchant_id"`
	DepartmentID uint   `json:"department_id"`
	CardNumber   string `json:"card_number"`
	Amount       int    `json:"amount"`
	TraceID      string `json:"trace_id"`
	RecordID     string `json:"record_id"`
}

// TestResult 测试结果
type TestResult struct {
	TotalRequests     int64         `json:"total_requests"`
	SuccessfulRequests int64        `json:"successful_requests"`
	FailedRequests    int64         `json:"failed_requests"`
	AvgResponseTime   time.Duration `json:"avg_response_time"`
	MaxResponseTime   time.Duration `json:"max_response_time"`
	MinResponseTime   time.Duration `json:"min_response_time"`
	RequestsPerSecond float64       `json:"requests_per_second"`
	ErrorRate         float64       `json:"error_rate"`
	TestDuration      time.Duration `json:"test_duration"`
}

// StressTestConfig 压力测试配置
type StressTestConfig struct {
	BaseURL           string        `json:"base_url"`
	ConcurrentUsers   int           `json:"concurrent_users"`
	RequestsPerUser   int           `json:"requests_per_user"`
	RequestInterval   time.Duration `json:"request_interval"`
	TestTimeout       time.Duration `json:"test_timeout"`
	MerchantID        uint          `json:"merchant_id"`
	DepartmentID      uint          `json:"department_id"`
}

// StressTester 压力测试器
type StressTester struct {
	config      StressTestConfig
	httpClient  *http.Client
	
	// 统计计数器
	totalRequests     int64
	successRequests   int64
	failedRequests    int64
	
	// 响应时间统计
	responseTimes     []time.Duration
	responseTimesMux  sync.Mutex
	
	// 错误统计
	errors           map[string]int64
	errorsMux        sync.RWMutex
}

// NewStressTester 创建压力测试器
func NewStressTester(config StressTestConfig) *StressTester {
	return &StressTester{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        200,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
		},
		errors: make(map[string]int64),
	}
}

// RunStressTest 运行压力测试
func (st *StressTester) RunStressTest() (*TestResult, error) {
	log.Printf("开始压力测试: %d并发用户, 每用户%d请求", st.config.ConcurrentUsers, st.config.RequestsPerUser)
	
	startTime := time.Now()
	
	// 创建上下文和取消函数
	ctx, cancel := context.WithTimeout(context.Background(), st.config.TestTimeout)
	defer cancel()
	
	// 创建等待组
	var wg sync.WaitGroup
	
	// 启动并发用户
	for i := 0; i < st.config.ConcurrentUsers; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			st.runUserRequests(ctx, userID)
		}(i)
	}
	
	// 等待所有用户完成
	wg.Wait()
	
	endTime := time.Now()
	testDuration := endTime.Sub(startTime)
	
	// 计算结果
	result := st.calculateResults(testDuration)
	
	log.Printf("压力测试完成: 总请求=%d, 成功=%d, 失败=%d, 平均响应时间=%v, QPS=%.2f",
		result.TotalRequests, result.SuccessfulRequests, result.FailedRequests,
		result.AvgResponseTime, result.RequestsPerSecond)
	
	return result, nil
}

// runUserRequests 运行单个用户的请求
func (st *StressTester) runUserRequests(ctx context.Context, userID int) {
	for i := 0; i < st.config.RequestsPerUser; i++ {
		select {
		case <-ctx.Done():
			return
		default:
			st.sendBindCardRequest(userID, i)
			
			// 请求间隔
			if st.config.RequestInterval > 0 {
				time.Sleep(st.config.RequestInterval)
			}
		}
	}
}

// sendBindCardRequest 发送绑卡请求
func (st *StressTester) sendBindCardRequest(userID, requestID int) {
	atomic.AddInt64(&st.totalRequests, 1)
	
	// 构建请求
	request := BindCardRequest{
		MerchantID:   st.config.MerchantID,
		DepartmentID: st.config.DepartmentID,
		CardNumber:   fmt.Sprintf("*************%03d%03d", userID, requestID),
		Amount:       1000 + (userID*100 + requestID), // 变化的金额
		TraceID:      fmt.Sprintf("stress_test_%d_%d_%d", userID, requestID, time.Now().UnixNano()),
		RecordID:     fmt.Sprintf("record_%d_%d_%d", userID, requestID, time.Now().UnixNano()),
	}
	
	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		st.recordError("marshal_error", err)
		atomic.AddInt64(&st.failedRequests, 1)
		return
	}
	
	// 发送HTTP请求
	startTime := time.Now()
	
	resp, err := st.httpClient.Post(
		st.config.BaseURL+"/api/v1/bind-card",
		"application/json",
		bytes.NewBuffer(requestBody),
	)
	
	responseTime := time.Since(startTime)
	st.recordResponseTime(responseTime)
	
	if err != nil {
		st.recordError("http_error", err)
		atomic.AddInt64(&st.failedRequests, 1)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		st.recordError("read_error", err)
		atomic.AddInt64(&st.failedRequests, 1)
		return
	}
	
	// 检查响应状态
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		atomic.AddInt64(&st.successRequests, 1)
		log.Printf("用户%d请求%d成功: 状态=%d, 响应时间=%v", userID, requestID, resp.StatusCode, responseTime)
	} else {
		st.recordError(fmt.Sprintf("status_%d", resp.StatusCode), fmt.Errorf("status: %d, body: %s", resp.StatusCode, string(body)))
		atomic.AddInt64(&st.failedRequests, 1)
		log.Printf("用户%d请求%d失败: 状态=%d, 响应时间=%v, 错误=%s", userID, requestID, resp.StatusCode, responseTime, string(body))
	}
}

// recordResponseTime 记录响应时间
func (st *StressTester) recordResponseTime(duration time.Duration) {
	st.responseTimesMux.Lock()
	defer st.responseTimesMux.Unlock()
	st.responseTimes = append(st.responseTimes, duration)
}

// recordError 记录错误
func (st *StressTester) recordError(errorType string, err error) {
	st.errorsMux.Lock()
	defer st.errorsMux.Unlock()
	st.errors[errorType]++
	log.Printf("错误记录: %s - %v", errorType, err)
}

// calculateResults 计算测试结果
func (st *StressTester) calculateResults(testDuration time.Duration) *TestResult {
	st.responseTimesMux.Lock()
	defer st.responseTimesMux.Unlock()
	
	result := &TestResult{
		TotalRequests:     atomic.LoadInt64(&st.totalRequests),
		SuccessfulRequests: atomic.LoadInt64(&st.successRequests),
		FailedRequests:    atomic.LoadInt64(&st.failedRequests),
		TestDuration:      testDuration,
	}
	
	// 计算错误率
	if result.TotalRequests > 0 {
		result.ErrorRate = float64(result.FailedRequests) / float64(result.TotalRequests) * 100
	}
	
	// 计算QPS
	if testDuration.Seconds() > 0 {
		result.RequestsPerSecond = float64(result.TotalRequests) / testDuration.Seconds()
	}
	
	// 计算响应时间统计
	if len(st.responseTimes) > 0 {
		var total time.Duration
		result.MinResponseTime = st.responseTimes[0]
		result.MaxResponseTime = st.responseTimes[0]
		
		for _, rt := range st.responseTimes {
			total += rt
			if rt < result.MinResponseTime {
				result.MinResponseTime = rt
			}
			if rt > result.MaxResponseTime {
				result.MaxResponseTime = rt
			}
		}
		
		result.AvgResponseTime = total / time.Duration(len(st.responseTimes))
	}
	
	return result
}

// PrintErrorSummary 打印错误摘要
func (st *StressTester) PrintErrorSummary() {
	st.errorsMux.RLock()
	defer st.errorsMux.RUnlock()
	
	if len(st.errors) == 0 {
		log.Println("没有错误发生")
		return
	}
	
	log.Println("错误摘要:")
	for errorType, count := range st.errors {
		log.Printf("  %s: %d次", errorType, count)
	}
}

func main() {
	// 配置压力测试
	config := StressTestConfig{
		BaseURL:         "http://localhost:21000", // 根据实际情况修改
		ConcurrentUsers: 100,                      // 100并发用户
		RequestsPerUser: 10,                       // 每用户10个请求
		RequestInterval: 100 * time.Millisecond,   // 请求间隔100ms
		TestTimeout:     5 * time.Minute,          // 5分钟超时
		MerchantID:      1,                        // 测试商户ID
		DepartmentID:    1,                        // 测试部门ID
	}
	
	// 创建压力测试器
	tester := NewStressTester(config)
	
	// 运行测试
	result, err := tester.RunStressTest()
	if err != nil {
		log.Fatalf("压力测试失败: %v", err)
	}
	
	// 打印结果
	fmt.Println("\n=== 压力测试结果 ===")
	fmt.Printf("总请求数: %d\n", result.TotalRequests)
	fmt.Printf("成功请求: %d\n", result.SuccessfulRequests)
	fmt.Printf("失败请求: %d\n", result.FailedRequests)
	fmt.Printf("成功率: %.2f%%\n", float64(result.SuccessfulRequests)/float64(result.TotalRequests)*100)
	fmt.Printf("错误率: %.2f%%\n", result.ErrorRate)
	fmt.Printf("平均响应时间: %v\n", result.AvgResponseTime)
	fmt.Printf("最大响应时间: %v\n", result.MaxResponseTime)
	fmt.Printf("最小响应时间: %v\n", result.MinResponseTime)
	fmt.Printf("QPS: %.2f\n", result.RequestsPerSecond)
	fmt.Printf("测试持续时间: %v\n", result.TestDuration)
	
	// 打印错误摘要
	tester.PrintErrorSummary()
	
	// 检查是否达到性能目标
	fmt.Println("\n=== 性能目标检查 ===")
	
	successRate := float64(result.SuccessfulRequests) / float64(result.TotalRequests) * 100
	if successRate >= 95 {
		fmt.Printf("✅ 成功率目标达成: %.2f%% >= 95%%\n", successRate)
	} else {
		fmt.Printf("❌ 成功率目标未达成: %.2f%% < 95%%\n", successRate)
	}
	
	if result.AvgResponseTime <= 3*time.Second {
		fmt.Printf("✅ 响应时间目标达成: %v <= 3s\n", result.AvgResponseTime)
	} else {
		fmt.Printf("❌ 响应时间目标未达成: %v > 3s\n", result.AvgResponseTime)
	}
	
	if result.RequestsPerSecond >= 30 { // 100并发，每用户10请求，期望至少30 QPS
		fmt.Printf("✅ QPS目标达成: %.2f >= 30\n", result.RequestsPerSecond)
	} else {
		fmt.Printf("❌ QPS目标未达成: %.2f < 30\n", result.RequestsPerSecond)
	}
}
