# 沃尔玛绑卡网关配置文件

# 服务器配置
server:
  port: 21000
  host: "0.0.0.0"
  mode: "debug" # debug, release
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"
  max_header_bytes: 1048576

# 并发配置 - 高并发优化
concurrency:
  max_goroutines: 2000 # 支持更多协程
  worker_pool_size: 100 # 大幅增加工作池支持100并发
  queue_buffer_size: 1000 # 增加队列缓冲
  batch_size: 50 # 增加批处理大小提高吞吐量
  batch_timeout: "2s" # 适当增加超时
  graceful_shutdown_timeout: "30s"

  # 新增协程池管理配置
  goroutine_pool:
    core_size: 50 # 核心协程数
    max_size: 200 # 最大协程数
    keep_alive: "5m" # 空闲协程保活时间
    queue_capacity: 2000 # 任务队列容量

  # 背压控制
  backpressure:
    enabled: true
    max_pending: 500 # 最大待处理任务数
    reject_policy: "oldest" # 拒绝策略：oldest, newest, caller_runs

# Go运行时优化配置
runtime:
  # 垃圾回收优化
  gc:
    target_percent: 100 # GOGC=100，平衡内存使用和GC频率
    max_procs: 0 # 使用所有可用CPU核心

  # 内存优化
  memory:
    soft_memory_limit: "2GB" # 软内存限制
    debug_gc: false # 生产环境关闭GC调试

  # 协程调度优化
  scheduler:
    max_idle_conns_per_host: 100 # HTTP客户端连接复用
    idle_conn_timeout: "90s"

# 性能监控配置
performance:
  # 指标收集
  metrics:
    enabled: true
    interval: "30s"
    retention: "24h"

  # 健康检查
  health_check:
    enabled: true
    interval: "10s"
    timeout: "5s"

  # 性能分析
  profiling:
    enabled: true # 启用pprof
    cpu_profile: false # 生产环境关闭CPU分析
    memory_profile: true # 启用内存分析
    block_profile: true # 启用阻塞分析

# 数据库配置 - 高并发优化
database:
  type: "mysql"
  host: "localhost"
  port: 3306
  user: "root"
  password: "7c222fb2927d828af22f592134e8932480637c0d"
  db_name: "walmart_card_db"
  charset: "utf8mb4"
  max_open_conns: 150 # 增加到150个连接支持更高并发
  max_idle_conns: 50 # 增加空闲连接数减少连接创建延时
  conn_max_lifetime: "1h" # 1小时生命周期，平衡性能和资源
  conn_max_idle_time: "30m" # 30分钟空闲时间，合理回收
  query_timeout: "30s"
  exec_timeout: "30s"

  # 新增高并发优化配置
  performance:
    prepare_stmt_cache_size: 1000 # 预编译语句缓存
    interpolate_params: true # 参数插值优化
    multi_statements: true # 支持多语句
    read_timeout: "30s"
    write_timeout: "30s"

  # 连接池监控
  monitoring:
    log_slow_queries: true
    slow_query_threshold: "1s"
    log_connection_stats: true
    stats_interval: "30s"

# Redis配置 - 高并发优化连接池
redis:
  host: "localhost"
  port: 6379
  password: "7c222fb2927d828af22f592134e8932480637c0d"
  db: 0
  pool_size: 200 # 大幅增加连接池大小支持100+并发
  min_idle_conns: 50 # 增加最小空闲连接减少延时
  max_retries: 5 # 增加重试次数
  dial_timeout: "5s"
  read_timeout: "5s" # 增加读超时
  write_timeout: "5s" # 增加写超时
  pool_timeout: "10s" # 增加池超时防止连接等待
  idle_timeout: "10m" # 增加空闲超时
  idle_check_frequency: "1m" # 1分钟检查频率
  pipeline_size: 100 # 增加管道大小提高吞吐量

  # 高并发性能优化
  performance:
    min_retry_backoff: "100ms" # 最小重试间隔
    max_retry_backoff: "1s" # 最大重试间隔
    context_timeout_enabled: true # 启用上下文超时

  # 连接池监控
  monitoring:
    enable_stats: true
    stats_interval: "30s"
    log_pool_stats: true

# RabbitMQ配置
rabbitmq:
  host: localhost
  port: 5672
  user: walmart_card
  password: 7c222fb2927d828af22f592134e8932480637c0d
  vhost: /walmart_card
  max_connections: 10
  max_channels_per_conn: 100
  batch_publish_size: 100
  batch_timeout: "1s"
  confirm_mode: true
  connection_pool_size: 5
  channel_pool_size: 50
  consumer_prefetch_count: 10 # 高并发预取消息数
  consumer_concurrency: 5 # 高并发消费者数量
  max_retries: 3 # 最大重试次数
  retry_delay: "1s" # 重试延迟
  queues:
    bind_card: "bind_card_queue"
    callback: "bind_card_callback_queue"

  # 重连配置
  reconnect_max_retries: 5 # 最大重连次数
  reconnect_initial_delay: "2s" # 初始重连延迟
  reconnect_max_delay: "30s" # 最大重连延迟
  reconnect_multiplier: 1.5 # 重连延迟倍数

  # 队列管理配置
  queue_management:
    check_queue_existence: true # 启动时检查队列是否存在
    auto_create_queues: false # 禁止自动创建队列，避免临时队列
    strict_mode: true # 启用严格模式，完全禁止队列声明
    queue_declare_timeout: "10s" # 队列声明超时时间

# 日志配置
logging:
  level: "info" # debug, info, warn, error
  format: "json" # json, console
  output: "stdout" # stdout, stderr, file path
  fields:
    service: "walmart-bind-card-processor"
    version: "v0.0.1"

# 重试策略配置
retry_strategy:
  # 绑卡重试配置
  bind_card:
    max_attempts: 3
    initial_delay: "1s"
    max_delay: "10s"
    backoff_multiplier: 2.0

    # 可重试的错误（普通重试，不切换CK）
    retryable_errors:
      - "网络超时"
      - "连接超时"
      - "临时不可用"
      - "请求超时"
      - "timeout"
      - "connection.*reset"
      - "网络连接异常"
      - "服务暂时不可用"
      - "系统繁忙，请稍后再试"
      - "服务器内部错误"
      - "网络异常"
      - "连接失败"
      - "获取分布式锁超时，系统繁忙"
      - "获取分布式锁失败"
      - "获取提交锁超时"
      - "Redis连接失败"
      - "分布式锁超时"

    # 需要切换CK重试的错误（禁用当前CK，切换新CK重试）
    ck_switch_errors:
      - "请先去登录"
      - "去登录"
      - "您绑卡已超过单日20张限制"
      - "单日20张限制"
      - "请明天再试"
      - "错误次数过多,请稍后再试"
      - "错误次数过多"
      - "数据异常"
      - "服务器繁忙"
      - "账号异常"
      - "操作频繁"
      - "需要重新登录"
      - "登录状态失效"
      - "会话过期"
      - "用户未登录"
      - "登录超时"
      - "errorcode.*203"
      - "errorcode.*110224"
      - "errorcode.*110134"
      - "errorcode.*110444"
      - "errorcode.*200"

    # 不可重试的错误（直接失败，不重试）
    non_retryable_errors:
      - "同一张实体卡只允许绑定到一个微信账户"
      - "该电子卡已被其他用户绑定"
      - "卡已被其他用户绑定"
      - "余额不足"
      - "卡片已过期"
      - "密码错误"
      - "卡号格式错误"
      - "暂不支持该类型卡"
      - "卡片无效"
      - "请求过期"
      - "需要隐式登录"
      - "绑卡金额与真实金额不符"
      - "金额不符"
      - "卡号不存在"
      - "卡片状态异常"
      - "卡片已冻结"
      - "卡片已注销"
      - "超出绑卡限制"
      - "不支持的卡类型"
      - "errorcode.*10131"
      - "errorcode.*5042"
      - "errorcode.*5041"
      - "errorcode.*506"
      - "errorcode.*110445"
      - "errorcode.*9999"

  # 金额查询重试配置
  balance_query:
    max_attempts: 3
    initial_delay: "500ms"
    max_delay: "5s"
    backoff_multiplier: 2.0

    # 金额查询只能重试技术性错误，不能切换CK
    # 因为金额查询必须使用与绑卡相同的CK来查询同一用户的卡包
    retryable_errors:
      - "网络超时"
      - "连接超时"
      - "临时不可用"
      - "请求超时"
      - "timeout"
      - "connection.*reset"
      - "网络连接异常"
      - "服务器内部错误"
      - "网络异常"
      - "连接失败"

    # 金额查询的业务错误都不可重试
    non_retryable_errors:
      - "请先去登录"
      - "登录状态失效"
      - "用户未登录"
      - "会话过期"
      - "卡号不存在"
      - "卡片无效"
      - "权限不足"
      - "账号异常"
      - "数据异常"
      - "errorcode.*203"
      - "errorcode.*110224"
      - "errorcode.*110134"

# 功能模块开关配置
features:
  # 绑卡模块配置
  bind_card:
    enabled: true # 启用绑卡业务, 如果为false，则不处理绑卡业务逻辑

# 沃尔玛配置
walmart:
  # 监控配置
  monitoring:
    enable_metrics: true
    enable_detailed_logging: true
    slow_request_threshold: 5000

# 高并发CK管理配置
ck_management:
  # 分布式锁配置
  distributed_lock:
    timeout: "15s" # 增加锁超时时间，减少高并发下的超时
    retry_interval: "200ms" # 增加重试间隔，减少锁竞争
    max_retries: 5 # 增加重试次数
    acquire_timeout: "30s" # 获取锁的总超时时间
    backoff_multiplier: 1.5 # 指数退避倍数

  # CK状态同步配置
  status_sync:
    cache_expiry: "5m"
    sync_interval: "10s"
    failure_threshold: 3
    recovery_interval: "30s"
    event_buffer_size: 1000

  # CK监控配置
  monitoring:
    metrics_retention: "24h"
    log_retention: "168h" # 7天
    batch_size: 100
    flush_interval: "30s"

    # 告警阈值
    alert_thresholds:
      success_rate: 0.95 # 95%成功率
      response_time: "5s"
      consecutive_failures: 3
      error_rate: 0.05 # 5%错误率

  # 权重算法配置
  weight_algorithm:
    cache_refresh_interval: "1m"
    lock_timeout: "5s"
    max_retries: 3
    circuit_breaker_threshold: 5

  # CK预占用配置
  preoccupation:
    timeout: "5m"
    cleanup_interval: "1m"
    max_concurrent: 1000
