"""
并发安全的CK选择服务
解决高并发下CK选择和预占用的竞态条件问题
"""

import asyncio
import time
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from contextlib import asynccontextmanager

from app.models.walmart_ck import WalmartCK
from app.core.logging import get_logger
from app.core.redis import get_redis

logger = get_logger(__name__)


class ConcurrentSafeCKService:
    """并发安全的CK选择服务"""

    def __init__(self):
        self.redis_client = None
        self._lock_timeout = 10  # Redis锁超时时间（秒）
        self._selection_timeout = 5  # CK选择超时时间（秒）

    async def _get_redis_client(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            self.redis_client = await get_redis()
        return self.redis_client
    
    @asynccontextmanager
    async def _distributed_lock(self, lock_key: str, lock_value: str):
        """分布式锁上下文管理器"""
        redis_client = await self._get_redis_client()
        acquired = False
        try:
            # 尝试获取锁
            acquired = await redis_client.set(
                lock_key, lock_value,
                ex=self._lock_timeout,
                nx=True
            )

            if not acquired:
                raise RuntimeError(f"获取分布式锁失败: {lock_key}")

            logger.debug(f"获取分布式锁成功: {lock_key}")
            yield

        finally:
            if acquired:
                # 释放锁（使用Lua脚本确保原子性）
                lua_script = """
                if redis.call("get", KEYS[1]) == ARGV[1] then
                    return redis.call("del", KEYS[1])
                else
                    return 0
                end
                """
                await redis_client.eval(lua_script, 1, lock_key, lock_value)
                logger.debug(f"释放分布式锁: {lock_key}")
    
    async def select_and_preoccupy_ck_safe(
        self,
        db: AsyncSession,
        merchant_id: int,
        department_id: Optional[int] = None,
        exclude_ids: Optional[List[int]] = None,
        bind_context: Optional[Dict[str, Any]] = None
    ) -> Optional[WalmartCK]:
        """
        并发安全的CK选择和预占用
        
        Args:
            db: 异步数据库会话
            merchant_id: 商户ID
            department_id: 部门ID（可选）
            exclude_ids: 排除的CK ID列表
            bind_context: 绑卡上下文信息
            
        Returns:
            预占用成功的CK对象或None
        """
        if not merchant_id:
            logger.error("商户ID不能为空")
            return None
        
        trace_id = bind_context.get('trace_id', 'unknown') if bind_context else 'unknown'
        record_id = bind_context.get('record_id', 'unknown') if bind_context else 'unknown'
        
        # 构建分布式锁键
        lock_key = f"ck_selection:merchant_{merchant_id}"
        if department_id:
            lock_key += f":dept_{department_id}"
        
        lock_value = f"{trace_id}:{record_id}:{int(time.time() * 1000)}"
        
        try:
            # 使用分布式锁保护CK选择过程
            async with self._distributed_lock(lock_key, lock_value):
                logger.info(f"开始并发安全CK选择: merchant_id={merchant_id}, "
                           f"department_id={department_id}, trace_id={trace_id}")
                
                # 在锁保护下执行CK选择和预占用
                return await self._select_and_preoccupy_with_transaction(
                    db, merchant_id, department_id, exclude_ids, trace_id
                )
                
        except Exception as e:
            logger.error(f"并发安全CK选择失败: {e}, trace_id={trace_id}")
            return None
    
    async def _select_and_preoccupy_with_transaction(
        self,
        db: AsyncSession,
        merchant_id: int,
        department_id: Optional[int],
        exclude_ids: Optional[List[int]],
        trace_id: str
    ) -> Optional[WalmartCK]:
        """在事务中执行CK选择和预占用"""
        
        try:
            # 开始数据库事务
            async with db.begin():
                # 构建查询条件
                conditions = [
                    WalmartCK.merchant_id == merchant_id,
                    WalmartCK.active == True,
                    WalmartCK.is_deleted == False,
                    WalmartCK.bind_count < WalmartCK.total_limit
                ]
                
                if department_id:
                    conditions.append(WalmartCK.department_id == department_id)
                
                if exclude_ids:
                    conditions.append(~WalmartCK.id.in_(exclude_ids))
                
                # 使用行锁查询可用CK（选择前3个候选）
                stmt = select(WalmartCK).where(
                    and_(*conditions)
                ).with_for_update().order_by(
                    WalmartCK.bind_count.asc(),
                    WalmartCK.last_bind_time.asc()
                ).limit(3)
                
                result = await db.execute(stmt)
                candidates = result.scalars().all()
                
                if not candidates:
                    logger.warning(f"没有可用的CK: merchant_id={merchant_id}, "
                                 f"department_id={department_id}, trace_id={trace_id}")
                    return None
                
                # 从候选中选择一个进行预占用
                for candidate in candidates:
                    # 双重检查：确保预占用后不会超过限制
                    if candidate.bind_count + 1 <= candidate.total_limit:
                        # 原子性更新bind_count
                        update_stmt = update(WalmartCK).where(
                            and_(
                                WalmartCK.id == candidate.id,
                                WalmartCK.bind_count == candidate.bind_count  # 乐观锁
                            )
                        ).values(
                            bind_count=WalmartCK.bind_count + 1,
                            last_bind_time=int(time.time())
                        )
                        
                        update_result = await db.execute(update_stmt)
                        
                        if update_result.rowsaffected > 0:
                            # 预占用成功，刷新对象状态
                            await db.refresh(candidate)
                            
                            logger.info(f"CK预占用成功: ck_id={candidate.id}, "
                                       f"bind_count={candidate.bind_count}/{candidate.total_limit}, "
                                       f"trace_id={trace_id}")
                            
                            # 检查是否需要自动禁用
                            if candidate.bind_count >= candidate.total_limit:
                                candidate.active = False
                                logger.info(f"CK已达到限制，自动禁用: ck_id={candidate.id}")
                            
                            return candidate
                        else:
                            logger.debug(f"CK预占用失败（并发冲突）: ck_id={candidate.id}, trace_id={trace_id}")
                            continue
                
                logger.warning(f"所有候选CK预占用失败: merchant_id={merchant_id}, trace_id={trace_id}")
                return None
                
        except Exception as e:
            logger.error(f"CK选择事务执行失败: {e}, trace_id={trace_id}")
            raise
    
    async def release_ck_preoccupation(
        self,
        db: AsyncSession,
        ck_id: int,
        success: bool,
        trace_id: str = "unknown"
    ) -> bool:
        """
        释放CK预占用
        
        Args:
            db: 异步数据库会话
            ck_id: CK ID
            success: 绑卡是否成功
            trace_id: 追踪ID
            
        Returns:
            释放是否成功
        """
        try:
            async with db.begin():
                if not success:
                    # 绑卡失败，回滚bind_count
                    update_stmt = update(WalmartCK).where(
                        WalmartCK.id == ck_id
                    ).values(
                        bind_count=WalmartCK.bind_count - 1,
                        active=True  # 重新激活
                    )
                    
                    result = await db.execute(update_stmt)
                    
                    if result.rowsaffected > 0:
                        logger.info(f"CK预占用回滚成功: ck_id={ck_id}, trace_id={trace_id}")
                        return True
                    else:
                        logger.warning(f"CK预占用回滚失败: ck_id={ck_id}, trace_id={trace_id}")
                        return False
                else:
                    logger.info(f"绑卡成功，保持CK预占用: ck_id={ck_id}, trace_id={trace_id}")
                    return True
                    
        except Exception as e:
            logger.error(f"释放CK预占用失败: {e}, ck_id={ck_id}, trace_id={trace_id}")
            return False


# 全局实例
concurrent_safe_ck_service = ConcurrentSafeCKService()
