package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"walmart-bind-card-processor/internal/config"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HTTPServer HTTP服务器
type HTTPServer struct {
	server             *http.Server
	logger             *zap.Logger
	config             *config.Config
	healthCheckService *HealthCheckService
	performanceMonitor *PerformanceMonitor
}

// NewHTTPServer 创建HTTP服务器
func NewHTTPServer(
	config *config.Config,
	logger *zap.Logger,
	db *gorm.DB,
	redis *redis.Client,
	performanceMonitor *PerformanceMonitor,
) *HTTPServer {
	// 创建健康检查服务
	healthCheckService := NewHealthCheckService(db, redis, config, logger, performanceMonitor)

	// 创建HTTP服务器
	httpServer := &HTTPServer{
		logger:             logger,
		config:             config,
		healthCheckService: healthCheckService,
		performanceMonitor: performanceMonitor,
	}

	// 设置路由
	mux := http.NewServeMux()
	httpServer.setupRoutes(mux)

	// 创建HTTP服务器
	httpServer.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", config.Server.Port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	return httpServer
}

// setupRoutes 设置路由
func (h *HTTPServer) setupRoutes(mux *http.ServeMux) {
	// 健康检查端点
	mux.HandleFunc("/health", h.healthCheckService.ServeHTTP)
	mux.HandleFunc("/health/live", h.handleLivenessCheck)
	mux.HandleFunc("/health/ready", h.handleReadinessCheck)
	
	// 性能监控端点
	mux.HandleFunc("/metrics", h.handleMetrics)
	mux.HandleFunc("/metrics/performance", h.handlePerformanceMetrics)
	
	// 系统信息端点
	mux.HandleFunc("/info", h.handleSystemInfo)
	mux.HandleFunc("/status", h.handleStatus)
	
	// 根路径
	mux.HandleFunc("/", h.handleRoot)
}

// handleLivenessCheck 处理存活检查
func (h *HTTPServer) handleLivenessCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	liveness := h.healthCheckService.GetLivenessCheck()
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(liveness)
}

// handleReadinessCheck 处理就绪检查
func (h *HTTPServer) handleReadinessCheck(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	w.Header().Set("Content-Type", "application/json")
	
	readiness := h.healthCheckService.GetReadinessCheck(ctx)
	
	if ready, ok := readiness["ready"].(bool); ok && ready {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	json.NewEncoder(w).Encode(readiness)
}

// handleMetrics 处理指标查询
func (h *HTTPServer) handleMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	if h.performanceMonitor == nil {
		http.Error(w, "Performance monitor not available", http.StatusServiceUnavailable)
		return
	}
	
	metrics := h.performanceMonitor.GetCurrentMetrics()
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(metrics)
}

// handlePerformanceMetrics 处理性能指标查询
func (h *HTTPServer) handlePerformanceMetrics(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	if h.performanceMonitor == nil {
		http.Error(w, "Performance monitor not available", http.StatusServiceUnavailable)
		return
	}
	
	// 获取查询参数
	query := r.URL.Query()
	timeRange := query.Get("range")
	if timeRange == "" {
		timeRange = "1h" // 默认1小时
	}
	
	// 解析时间范围
	var duration time.Duration
	switch timeRange {
	case "5m":
		duration = 5 * time.Minute
	case "15m":
		duration = 15 * time.Minute
	case "1h":
		duration = 1 * time.Hour
	case "24h":
		duration = 24 * time.Hour
	default:
		duration = 1 * time.Hour
	}
	
	// 获取性能摘要
	summary, err := h.performanceMonitor.GetPerformanceSummary(context.Background(), duration)
	if err != nil {
		h.logger.Error("获取性能摘要失败", zap.Error(err))
		http.Error(w, "Failed to get performance summary", http.StatusInternalServerError)
		return
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(summary)
}

// handleSystemInfo 处理系统信息查询
func (h *HTTPServer) handleSystemInfo(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	info := map[string]interface{}{
		"service":     "walmart-bind-card-processor",
		"version":     "1.0.0",
		"build_time":  "2024-01-01T00:00:00Z", // 可以从构建信息获取
		"go_version":  "go1.21",               // 可以从runtime获取
		"environment": h.config.Server.Mode,
		"port":        h.config.Server.Port,
		"timestamp":   time.Now(),
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(info)
}

// handleStatus 处理状态查询
func (h *HTTPServer) handleStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	w.Header().Set("Content-Type", "application/json")
	
	// 获取健康状态
	health := h.healthCheckService.CheckHealth(ctx)
	
	// 简化的状态响应
	status := map[string]interface{}{
		"status":    health.Status,
		"timestamp": health.Timestamp,
		"uptime":    health.Uptime,
		"version":   health.Version,
		"components": func() map[string]string {
			components := make(map[string]string)
			for name, component := range health.Components {
				components[name] = string(component.Status)
			}
			return components
		}(),
	}
	
	// 根据状态设置HTTP状态码
	switch health.Status {
	case HealthStatusHealthy:
		w.WriteHeader(http.StatusOK)
	case HealthStatusDegraded:
		w.WriteHeader(http.StatusOK)
	case HealthStatusUnhealthy:
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	json.NewEncoder(w).Encode(status)
}

// handleRoot 处理根路径
func (h *HTTPServer) handleRoot(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}
	
	w.Header().Set("Content-Type", "application/json")
	
	response := map[string]interface{}{
		"service":   "walmart-bind-card-processor",
		"version":   "1.0.0",
		"status":    "running",
		"timestamp": time.Now(),
		"endpoints": []string{
			"/health",
			"/health/live",
			"/health/ready",
			"/metrics",
			"/metrics/performance",
			"/info",
			"/status",
		},
	}
	
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// Start 启动HTTP服务器
func (h *HTTPServer) Start() error {
	h.logger.Info("启动HTTP服务器", 
		zap.String("addr", h.server.Addr),
		zap.String("mode", h.config.Server.Mode))
	
	if err := h.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("HTTP服务器启动失败: %w", err)
	}
	
	return nil
}

// Stop 停止HTTP服务器
func (h *HTTPServer) Stop(ctx context.Context) error {
	h.logger.Info("停止HTTP服务器")
	
	return h.server.Shutdown(ctx)
}

// GetAddr 获取服务器地址
func (h *HTTPServer) GetAddr() string {
	return h.server.Addr
}
